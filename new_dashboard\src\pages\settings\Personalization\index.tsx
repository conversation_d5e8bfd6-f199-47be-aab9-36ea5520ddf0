
import { useState } from 'react';
import StepChannels from './components/StepChannels';
import { Box, Button, Flex, Heading } from '@chakra-ui/react';
import ProgressBar from './components/ProgressBar';
const steps = ['Channels', 'KPI Setup', 'Review'];

const Personalization = () => {
   
const [step, setStep]= useState(1);
  const [formData, setFormData] = useState({});

 const nextStep = () => setStep((s) => Math.min(s + 1, steps.length));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));




 const renderStep = () => {
    switch (step) {
      
      case 1:
        return <StepChannels data={formData} setData={setFormData} />;
     {/* case 2:
        return <StepKPISetup data={formData} setData={setFormData} />;
      case 3:
        return <StepReview data={formData} />;
      default:
        return null;*/}
    }
  };


return (
    <Box p={6} w="100%">
      <div className=' flex items-center gap-2 mb-4 font-medium font-normal font-poppins head5'>
       Customize Your AI CMO
      </div>
      <ProgressBar steps={steps} currentStep={step} />

     
    </Box>
  );















};

export default Personalization;
