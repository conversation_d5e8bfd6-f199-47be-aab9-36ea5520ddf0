//import { useColorMode } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { noZeroKPI } from '@/utils/strings/kpi-constants';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { ChartProp } from '../utils/interface';
import { getChartDateLabel, getFormattedVal, toHHMMSS } from '../utils/helpers';
import { KpiTimelineItem } from '../utils/interface';
import { useColorMode } from '@chakra-ui/react';
interface DualLineChartProps {
   data: ChartProp[];
}

const DualLineChart = ({ data }: DualLineChartProps) => {
   const { colorMode } = useColorMode();
   const [kpi1, kpi2] = data;
   const categories = getChartDateLabel(kpi1.value as KpiTimelineItem[]);

   const [chartData, setChartData] = useState({
      options: {},
      series: [] as ApexAxisChartSeries,
   });

   useEffect(() => {
      // const colors = ['#6F42C1', '#b0b0b0ff'];
      const colors = ['#7F56D9', '#157EF3'];
      //const colors = ['#3B82F6', '#10B981'];
      //const colors = ['#8B5CF6', '#F59E0B'];

      const options: ApexOptions = {
         chart: {
            id: 'dual-kpi-chart',
            type: 'area',
            toolbar: { show: false },
            zoom: { enabled: false },
         },
         colors,

         stroke: {
            curve: 'smooth',
            width: 2,
            dashArray: [0, 5],
         },
         dataLabels: {
            enabled: false,
         },
         xaxis: {
            categories,
            title: { text: 'Date' },
            labels: {
               show: false,
               style: { colors: colorMode === 'dark' ? '#FFFFFF' : '#000000' },
            },
         },
         yaxis: {
            title: { text: 'Performance' },
            labels: {
               show: true,
               style: { colors: colorMode === 'dark' ? '#FFFFFF' : '#000000' },
            },
         },
         grid: {
            show: true,
            strokeDashArray: 6,
         },
         legend: {
            show: true,
            position: 'top',
            horizontalAlign: 'right',
         },
         tooltip: {
            shared: true,
            intersect: false,
            y: {
               formatter: function (value: number) {
                  if (!value && noZeroKPI.includes(kpi1.kpiDetails.kpi_names))
                     return 'N/A';
                  return kpi1.kpiDetails.kpi_unit === 'time'
                     ? toHHMMSS(value)
                     : getFormattedVal(Math.round(value * 100) / 100);
               },
            },
         },
      };

      const series = [
         {
            name: kpi1.kpiDetails.kpi_display_name,
            data: (kpi1.value || []).map((v) => Number(v.kpi_value.toFixed(2))),
         },
         {
            name: kpi2.kpiDetails.kpi_display_name,
            data: (kpi2.value || []).map((v) => Number(v.kpi_value.toFixed(2))),
         },
      ];

      setChartData({ options, series });
   }, [data, colorMode]);

   return (
      <div style={{ width: '100%', height: '100%' }}>
         <Chart
            options={chartData.options as ApexOptions}
            series={chartData.series}
            type='area'
            height='220'
         />
      </div>
   );
};

export default DualLineChart;
