import { useState } from 'react';
import { Check } from 'lucide-react';
import { CHANNELS } from '../utils/channels';

interface StepChannelsProps {
  data: any;
  setData: (data: any) => void;
}

const StepChannels = ({ data, setData }: StepChannelsProps) => {
  const [selected, setSelected] = useState<string[]>(data.channels || []);

  const toggleChannel = (id: string) => {
    const updated = selected.includes(id)
      ? selected.filter((c) => c !== id)
      : [...selected, id];
    setSelected(updated);
    setData({ ...data, channels: updated });
  };

  return (
    <div className="w-full">
      {/* Section Heading */}
      <div className="mb-2 text-lg font-semibold text-charcoal">
        Select Channels
      </div>
      <div className="mb-6 text-sm text-steel">
        Choose the platforms you want to configure channel-specific KPIs for.
      </div>

      {/* Channel Cards */}
      <div className="flex flex-wrap gap-6">
        {CHANNELS.map((ch) => {
          const isSelected = selected.includes(ch.id);
          return (
            <div
              key={ch.id}
              onClick={() => toggleChannel(ch.id)}
              className={`relative flex flex-col items-center justify-center w-44 h-48 rounded-xl border-2 cursor-pointer transition-all duration-200 
                ${
                  isSelected
                    ? 'border-[#7F56D9] shadow-[0_0_1px_rgba(0,0,0,0.3),0_2px_10px_rgba(0,0,0,0.06)] bg-white'
                    : 'border-gray-200 bg-white hover:border-[#7F56D9]/60 hover:shadow-[0_0_1px_rgba(0,0,0,0.2),0_2px_8px_rgba(0,0,0,0.04)]'
                }`}
            >
              {/* Checkmark Indicator */}
              {isSelected && (
                <div className="absolute top-3 right-3 w-5 h-5 rounded-full bg-[#7F56D9] flex items-center justify-center shadow-sm">
                  <Check size={12} className="text-white" strokeWidth={3} />
                </div>
              )}

              {/* Channel Icon */}
              <img
                src={ch.icon}
                alt={ch.name}
                className="w-14 h-14 mb-4 object-contain"
              />

              {/* Channel Name */}
              <div
                className={`font-medium text-center text-sm ${
                  isSelected ? 'text-[#7F56D9]' : 'text-charcoal'
                }`}
              >
                {ch.name}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StepChannels;
