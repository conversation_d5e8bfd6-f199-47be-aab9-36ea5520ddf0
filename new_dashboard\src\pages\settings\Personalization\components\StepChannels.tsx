
import React, { useState } from 'react';

const StepChannels = ({ data, setData }: any) => {
   const [selectedChannels, setSelectedChannels] = useState<string[]>([]);  

   const handleChannelSelect = (channel: string) => {
      setSelectedChannels((prev) => {
         if (prev.includes(channel)) {
            return prev.filter((c) => c !== channel);
         } else {
            return [...prev, channel];
         }
      });
   };

   const handleNext = () => {
      setData((prev: any) => ({ ...prev, channels: selectedChannels }));
   };

   return (
      <div>
         <h2>Step 1: Select Channels</h2>
         <div>
            <label>
               <input
                  type='checkbox'
                  checked={selectedChannels.includes('channel1')}
                  onChange={() => handleChannelSelect('channel1')}
               />
               Channel 1
            </label>
            <label>
               <input
                  type='checkbox'
                  checked={selectedChannels.includes('channel2')}
                  onChange={() => handleChannelSelect('channel2')}
               />
               Channel 2
            </label>
            <label>
               <input
                  type='checkbox'
                  checked={selectedChannels.includes('channel3')}
                  onChange={() => handleChannelSelect('channel3')}
               />
               Channel 3
            </label>
         </div>
         <button onClick={handleNext}>Next</button>
      </div>
   );
};

export default StepChannels;
 